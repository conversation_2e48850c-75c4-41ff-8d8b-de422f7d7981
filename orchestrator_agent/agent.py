# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Orchestrator Agent - Intelligent Question Routing
Routes user questions to appropriate specialized agents using LLM-based decision making
"""

import logging
import sys
import os
from pathlib import Path
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set environment variables
os.environ.setdefault('DATABASE_URL', 'postgresql+asyncpg://postgres:<EMAIL>:5432/postgres')

class QuestionRouter:
    """Intelligent question router using LLM-based classification"""
    
    def __init__(self):
        self.classifier_agent = Agent(
            name="question_classifier",
            description="Classifies questions to determine appropriate agent routing",
            model=LiteLlm("gemini-2.0-flash"),
            instruction="""
            You are a question classifier that determines which specialized agent should handle a user's question.

            AVAILABLE AGENTS:
            1. **Q&A Agent**: Handles questions about Dropi company, services, academy, tutorials, and general company information
            2. **Orders Agent**: Handles questions about orders, sales, order management, statistics, order status, and commercial operations

            CLASSIFICATION RULES:
            - Analyze the intent and content of the question
            - Consider what type of information the user is seeking
            - Determine which agent has the appropriate knowledge/tools to answer

            RESPONSE FORMAT:
            Respond with EXACTLY one of these two options:
            - "QA_AGENT" (for Dropi knowledge questions)
            - "ORDERS_AGENT" (for order management questions)

            Do not provide explanations, just the classification.

            EXAMPLES:
            - "¿Qué es Dropi?" → QA_AGENT
            - "¿Cuántas órdenes hay?" → ORDERS_AGENT
            - "¿Cómo funciona Dropi Academy?" → QA_AGENT
            - "¿Qué órdenes están pendientes?" → ORDERS_AGENT
            - "¿Cuáles son los servicios de Dropi?" → QA_AGENT
            - "¿Cuáles son las estadísticas de ventas?" → ORDERS_AGENT
            """
        )
        
        # Import specialized agents
        self.qa_agent = None
        self.orders_agent = None
        self._load_specialized_agents()
    
    def _load_specialized_agents(self):
        """Load the specialized Q&A and Orders agents"""
        try:
            # Load Q&A Agent
            qa_path = Path(__file__).parent.parent / "qa_agent" / "src"
            if qa_path not in [Path(p) for p in sys.path]:
                sys.path.insert(0, str(qa_path))
            
            import agent as qa_module
            self.qa_agent = qa_module.root_agent
            logger.info("✅ Q&A Agent loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to load Q&A Agent: {e}")
        
        try:
            # Load Orders Agent
            orders_path = Path(__file__).parent.parent / "orders_agent_web"
            if orders_path not in [Path(p) for p in sys.path]:
                sys.path.insert(0, str(orders_path))
            
            # Clear any previous agent imports to avoid conflicts
            if 'agent' in sys.modules:
                del sys.modules['agent']
            
            import agent as orders_module
            self.orders_agent = orders_module.root_agent
            logger.info("✅ Orders Agent loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to load Orders Agent: {e}")
    
    async def classify_question(self, question: str) -> str:
        """Classify the question to determine appropriate agent"""
        try:
            classification_result = ""
            async for chunk in self.classifier_agent.run_async(question):
                classification_result += str(chunk)
            
            # Extract the classification from the response
            classification = classification_result.strip().upper()
            
            if "QA_AGENT" in classification:
                return "QA_AGENT"
            elif "ORDERS_AGENT" in classification:
                return "ORDERS_AGENT"
            else:
                # Default fallback - analyze keywords as backup
                question_lower = question.lower()
                if any(word in question_lower for word in ['orden', 'pedido', 'venta', 'estadística', 'pendiente', 'completado']):
                    return "ORDERS_AGENT"
                else:
                    return "QA_AGENT"
                    
        except Exception as e:
            logger.error(f"Error in question classification: {e}")
            # Default to Q&A agent on error
            return "QA_AGENT"
    
    async def route_question(self, question: str) -> str:
        """Route question to appropriate agent and return response"""
        try:
            # Classify the question
            agent_type = await self.classify_question(question)
            logger.info(f"🎯 Question classified as: {agent_type}")
            
            # Route to appropriate agent
            if agent_type == "QA_AGENT" and self.qa_agent:
                logger.info("📚 Routing to Q&A Agent")
                response_parts = []
                async for chunk in self.qa_agent.run_async(question):
                    response_parts.append(str(chunk))
                response = ''.join(response_parts)
                return f"[Q&A Agent] {response}"
                
            elif agent_type == "ORDERS_AGENT" and self.orders_agent:
                logger.info("🛒 Routing to Orders Agent")
                response_parts = []
                async for chunk in self.orders_agent.run_async(question):
                    response_parts.append(str(chunk))
                response = ''.join(response_parts)
                return f"[Orders Agent] {response}"
                
            else:
                return f"❌ Error: No suitable agent available for {agent_type}"
                
        except Exception as e:
            logger.error(f"Error in question routing: {e}")
            return f"❌ Error processing question: {str(e)}"

# Initialize the router
question_router = QuestionRouter()

# Create the main orchestrator agent
root_agent = Agent(
    name="orchestrator_agent",
    description="Intelligent orchestrator that routes questions to specialized agents",
    model=LiteLlm("gemini-2.0-flash"),
    instruction="""
    You are an intelligent orchestrator agent that automatically routes user questions to the appropriate specialized agent.

    AVAILABLE SPECIALIZED AGENTS:
    1. Q&A Agent: Handles questions about Dropi company, services, academy, and general information
    2. Orders Agent: Handles questions about orders, sales, order management, and commercial operations

    PROCESS:
    1. Analyze the user's question to understand its intent and domain
    2. Automatically route to the appropriate specialized agent
    3. Return the response from the specialized agent
    4. Maintain context and provide helpful responses

    You have access to intelligent routing capabilities that will automatically determine the best agent for each question.
    Always provide clear, helpful responses based on the specialized agent's expertise.
    """
)

# Add routing functionality to the orchestrator
async def handle_orchestrator_query(query: str) -> str:
    """Handle queries through the orchestrator with intelligent routing"""
    try:
        logger.info(f"🎭 Orchestrator received query: {query}")
        response = await question_router.route_question(query)
        logger.info(f"✅ Orchestrator response ready")
        return response
    except Exception as e:
        logger.error(f"❌ Orchestrator error: {e}")
        return f"Error: {str(e)}"

# Create a custom method for the orchestrator
async def orchestrator_run_async(query: str):
    """Custom run_async that uses intelligent routing"""
    response = await handle_orchestrator_query(query)
    # Yield the response as a single chunk
    yield response

# Add the method to the agent
root_agent.run_async = orchestrator_run_async

logger.info("🎭 Orchestrator Agent initialized successfully")

# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Orders Agent Web Interface
FastAPI application for the orders management agent
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging
import sys
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the orders agent
try:
    # Add the current directory to the path to import the agent
    import sys
    from pathlib import Path
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))

    from agent import root_agent
    logger.info("✅ Orders agent imported successfully")
except Exception as e:
    logger.error(f"❌ Failed to import orders agent: {e}")
    root_agent = None

app = FastAPI(title="Orders Management Agent", description="Web interface for orders management")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    error: str = None

@app.get("/", response_class=HTMLResponse)
async def get_chat_interface():
    """Serve the chat interface"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Orders Management Agent</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; color: #333; margin-bottom: 30px; }
            .chat-container { border: 1px solid #ddd; height: 400px; overflow-y: auto; padding: 10px; margin-bottom: 20px; background-color: #fafafa; border-radius: 5px; }
            .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
            .user-message { background-color: #e3f2fd; text-align: right; }
            .agent-message { background-color: #f1f8e9; }
            .input-container { display: flex; gap: 10px; }
            .message-input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            .send-button { padding: 10px 20px; background-color: #2196f3; color: white; border: none; border-radius: 5px; cursor: pointer; }
            .send-button:hover { background-color: #1976d2; }
            .examples { margin-top: 20px; }
            .example-button { margin: 5px; padding: 8px 15px; background-color: #f0f0f0; border: 1px solid #ddd; border-radius: 5px; cursor: pointer; }
            .example-button:hover { background-color: #e0e0e0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🛒 Orders Management Agent</h1>
                <p>Ask questions about orders, sales, and order management</p>
            </div>
            
            <div class="chat-container" id="chatContainer">
                <div class="message agent-message">
                    <strong>Orders Agent:</strong> ¡Hola! Soy tu asistente especializado en gestión de órdenes. Puedo ayudarte con consultas sobre órdenes, estadísticas de ventas, estados de pedidos y más. ¿En qué puedo ayudarte?
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" placeholder="Escribe tu pregunta sobre órdenes..." onkeypress="handleKeyPress(event)">
                <button class="send-button" onclick="sendMessage()">Enviar</button>
            </div>
            
            <div class="examples">
                <h3>Ejemplos de preguntas:</h3>
                <button class="example-button" onclick="setMessage('¿Cuántas órdenes hay?')">¿Cuántas órdenes hay?</button>
                <button class="example-button" onclick="setMessage('¿Qué órdenes están pendientes?')">¿Qué órdenes están pendientes?</button>
                <button class="example-button" onclick="setMessage('¿Cuáles son las estadísticas de órdenes?')">¿Cuáles son las estadísticas de órdenes?</button>
                <button class="example-button" onclick="setMessage('¿Qué órdenes hay de hoy?')">¿Qué órdenes hay de hoy?</button>
            </div>
        </div>

        <script>
            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }

            function setMessage(message) {
                document.getElementById('messageInput').value = message;
            }

            async function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (!message) return;

                const chatContainer = document.getElementById('chatContainer');
                
                // Add user message
                const userDiv = document.createElement('div');
                userDiv.className = 'message user-message';
                userDiv.innerHTML = `<strong>Usuario:</strong> ${message}`;
                chatContainer.appendChild(userDiv);
                
                // Clear input
                input.value = '';
                
                // Scroll to bottom
                chatContainer.scrollTop = chatContainer.scrollHeight;
                
                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ message: message })
                    });
                    
                    const data = await response.json();
                    
                    // Add agent response
                    const agentDiv = document.createElement('div');
                    agentDiv.className = 'message agent-message';
                    if (data.error) {
                        agentDiv.innerHTML = `<strong>Orders Agent (Error):</strong> ${data.error}`;
                    } else {
                        agentDiv.innerHTML = `<strong>Orders Agent:</strong> ${data.response}`;
                    }
                    chatContainer.appendChild(agentDiv);
                    
                } catch (error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'message agent-message';
                    errorDiv.innerHTML = `<strong>Orders Agent (Error):</strong> Error de conexión: ${error.message}`;
                    chatContainer.appendChild(errorDiv);
                }
                
                // Scroll to bottom
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.post("/chat", response_model=ChatResponse)
async def chat_with_agent(request: ChatRequest):
    """Handle chat requests with the orders agent"""
    if not root_agent:
        raise HTTPException(status_code=500, detail="Orders agent not available")
    
    try:
        logger.info(f"Processing query: {request.message}")
        
        # Use the correct method to interact with the agent
        if hasattr(root_agent, 'run_async'):
            # Handle async generator properly
            response_parts = []
            async for chunk in root_agent.run_async(request.message):
                response_parts.append(str(chunk))
            response = ''.join(response_parts)
        elif hasattr(root_agent, 'run_live'):
            # Handle live response
            response_parts = []
            for chunk in root_agent.run_live(request.message):
                response_parts.append(str(chunk))
            response = ''.join(response_parts)
        else:
            response = "Agent method not available"
        
        logger.info(f"Agent response: {response[:100]}...")
        return ChatResponse(response=str(response))
        
    except Exception as e:
        logger.error(f"Error processing request: {e}")
        return ChatResponse(response="", error=f"Error: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "agent_available": root_agent is not None,
        "agent_name": root_agent.name if root_agent else None
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
